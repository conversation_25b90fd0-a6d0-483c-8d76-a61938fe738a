/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Enhanced Color Palette */
    --primary-color: #FF4081;
    --secondary-color: #7C4DFF;
    --accent-color: #00BCD4;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --info-color: #2196F3;

    /* Tamil Nadu Inspired Colors */
    --temple-gold: #FFD700;
    --saffron: #FF9933;
    --peacock-blue: #005F73;
    --lotus-pink: #FF69B4;
    --turmeric-yellow: #FFCC02;
    --coconut-white: #F8F8FF;
    --earth-brown: #8B4513;

    /* Gradient Colors */
    --gradient-primary: linear-gradient(135deg, #FF4081, #7C4DFF);
    --gradient-secondary: linear-gradient(135deg, #00BCD4, #4CAF50);
    --gradient-accent: linear-gradient(135deg, #FFD700, #FF9800);
    --gradient-temple: linear-gradient(135deg, #FF9933, #FFD700);
    --gradient-nature: linear-gradient(135deg, #4CAF50, #8BC34A);
    --gradient-ocean: linear-gradient(135deg, #00BCD4, #03DAC6);

    /* Text Colors */
    --text-dark: #1A1A1A;
    --text-light: #666666;
    --text-muted: #9E9E9E;
    --white: #FFFFFF;

    /* Background Colors */
    --bg-primary: #FAFAFA;
    --bg-secondary: #F5F5F5;
    --bg-card: #FFFFFF;
    --bg-overlay: rgba(0, 0, 0, 0.5);

    /* Enhanced Shadows */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
    --shadow-glow: 0 0 20px rgba(255, 64, 129, 0.3);
    --shadow-neon: 0 0 30px rgba(124, 77, 255, 0.4);

    /* Border Radius */
    --border-radius-sm: 8px;
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;
    --border-radius-full: 50%;

    /* Enhanced Transitions */
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Animation Durations */
    --animation-fast: 0.3s;
    --animation-normal: 0.5s;
    --animation-slow: 0.8s;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

/* Enhanced Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 64, 129, 0.3); }
    50% { box-shadow: 0 0 30px rgba(255, 64, 129, 0.6); }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeInScale {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes rainbow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced Background Elements */
.bg-pattern {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 64, 129, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 77, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 188, 212, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 60% 70%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 90% 10%, rgba(76, 175, 80, 0.1) 0%, transparent 50%);
    z-index: -3;
    animation: float 6s ease-in-out infinite;
}

.bg-gradient {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(255, 64, 129, 0.05) 0%,
        rgba(124, 77, 255, 0.05) 25%,
        rgba(0, 188, 212, 0.05) 50%,
        rgba(76, 175, 80, 0.05) 75%,
        rgba(255, 215, 0, 0.05) 100%);
    background-size: 400% 400%;
    animation: rainbow 15s ease infinite;
    z-index: -2;
}

/* Animated Particles */
.bg-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 64, 129, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(124, 77, 255, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 188, 212, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 215, 0, 0.3), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(76, 175, 80, 0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 4s linear infinite;
    z-index: -1;
}

/* Floating Geometric Shapes */
.bg-pattern::after {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba(255, 64, 129, 0.1), rgba(124, 77, 255, 0.1));
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
    z-index: -1;
}

.app-container {
    display: flex;
    height: 100vh;
    max-width: 1600px;
    margin: 20px auto;
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInScale var(--animation-slow) ease-out;
}

.app-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.02;
    border-radius: var(--border-radius-xl);
    z-index: -1;
}

/* Enhanced Sidebar Styles */
.sidebar {
    width: 350px;
    background: var(--gradient-primary);
    color: var(--white);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    animation: slideInLeft var(--animation-normal) ease-out;
    box-shadow: inset -1px 0 0 rgba(255, 255, 255, 0.1);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="temple" patternUnits="userSpaceOnUse" width="30" height="30"><path d="M15 3L22 12H8L15 3ZM5 12H25V27H5V12Z" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23temple)"/></svg>');
    z-index: 0;
    animation: float 10s ease-in-out infinite;
}

.sidebar::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: rotate 20s linear infinite;
    z-index: 0;
}

.sidebar-header {
    padding: 40px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
    z-index: 2;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 20px;
    animation: fadeInScale var(--animation-normal) ease-out 0.2s;
}

.logo-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-glow);
    position: relative;
    overflow: hidden;
    animation: pulse 3s ease-in-out infinite;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: rotate 3s linear infinite;
}

.logo-icon i {
    position: relative;
    z-index: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.logo-text h1 {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 4px;
    background: linear-gradient(45deg, #FFD700, #FFF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: glow 2s ease-in-out infinite alternate;
}

.logo-text p {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 400;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sidebar-content {
    flex: 1;
    padding: 30px;
    position: relative;
    z-index: 2;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.sidebar-content::-webkit-scrollbar {
    width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.travel-stats {
    margin-bottom: 35px;
    background: rgba(255, 255, 255, 0.08);
    padding: 25px;
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideInUp var(--animation-normal) ease-out 0.3s;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 18px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-normal);
    animation: fadeInScale var(--animation-normal) ease-out;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-item:hover {
    transform: translateX(5px);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding-left: 15px;
    padding-right: 15px;
}

.stat-item i {
    width: 45px;
    height: 45px;
    background: var(--gradient-accent);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.stat-item i::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: rotate(45deg);
    transition: var(--transition-normal);
}

.stat-item:hover i {
    transform: scale(1.1) rotate(5deg);
    animation: pulse 1s ease-in-out infinite;
}

.stat-item:hover i::before {
    animation: rotate 1s linear infinite;
}

.stat-item div {
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.4rem;
    font-weight: 700;
    line-height: 1.2;
    background: linear-gradient(45deg, #FFD700, #FFF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 0.85rem;
    opacity: 0.9;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.quick-actions h3 {
    font-size: 1.1rem;
    margin-bottom: 25px;
    opacity: 0.95;
    font-weight: 600;
    text-align: center;
    background: linear-gradient(45deg, #FFD700, #FFF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    animation: slideInUp var(--animation-normal) ease-out 0.4s;
}

.action-btn {
    background: rgba(255, 255, 255, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.25);
    color: var(--white);
    padding: 18px 15px;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: var(--transition-bounce);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    font-size: 0.85rem;
    font-weight: 500;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-glow);
    border-color: rgba(255, 255, 255, 0.4);
}

.action-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.action-btn i {
    font-size: 1.4rem;
    transition: var(--transition-bounce);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.action-btn:hover i {
    transform: scale(1.2) rotate(5deg);
    animation: pulse 0.6s ease-in-out;
}

/* Individual button color themes */
.action-btn[data-topic="temples"] { border-left: 3px solid var(--temple-gold); }
.action-btn[data-topic="food"] { border-left: 3px solid var(--saffron); }
.action-btn[data-topic="transport"] { border-left: 3px solid var(--peacock-blue); }
.action-btn[data-topic="culture"] { border-left: 3px solid var(--lotus-pink); }
.action-btn[data-topic="places"] { border-left: 3px solid var(--success-color); }
.action-btn[data-topic="shopping"] { border-left: 3px solid var(--warning-color); }
.action-btn[data-topic="agriculture"] { border-left: 3px solid var(--success-color); }
.action-btn[data-topic="crops"] { border-left: 3px solid var(--turmeric-yellow); }
.action-btn[data-topic="heritage"] { border-left: 3px solid var(--temple-gold); }
.action-btn[data-topic="forest"] { border-left: 3px solid var(--success-color); }

/* Agricultural Analysis Section */
.agri-analysis {
    margin-top: 30px;
    padding: 20px;
    background: rgba(76, 175, 80, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.agri-analysis h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #2E7D32;
    font-weight: 600;
}

.agri-analysis p {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 15px;
    color: #4CAF50;
}

.agri-upload-btn {
    width: 100%;
    background: linear-gradient(135deg, #4CAF50, #66BB6A);
    border: none;
    color: var(--white);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.agri-upload-btn:hover {
    background: linear-gradient(135deg, #388E3C, #4CAF50);
    transform: translateY(-1px);
}

/* Heritage Section */
.heritage-section {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.heritage-section h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: var(--dark-red);
    font-weight: 600;
}

.heritage-section p {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.heritage-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: var(--white);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.heritage-btn:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: translateY(-1px);
}

/* Forest Analysis Section */
.forest-analysis {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(34, 139, 34, 0.1), rgba(46, 125, 50, 0.1));
    border-radius: var(--border-radius);
    border: 1px solid rgba(34, 139, 34, 0.2);
}

.forest-analysis h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #1B5E20;
    font-weight: 600;
}

.forest-analysis p {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 15px;
    color: #2E7D32;
}

.forest-upload-btn {
    width: 100%;
    background: linear-gradient(135deg, #2E7D32, #388E3C);
    border: none;
    color: var(--white);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.forest-upload-btn:hover {
    background: linear-gradient(135deg, #1B5E20, #2E7D32);
    transform: translateY(-1px);
}

/* Voice Assistant Section */
.voice-assistant {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(103, 58, 183, 0.1), rgba(156, 39, 176, 0.1));
    border-radius: var(--border-radius);
    border: 1px solid rgba(103, 58, 183, 0.2);
}

.voice-assistant h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #4A148C;
    font-weight: 600;
}

.voice-assistant p {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 15px;
    color: #6A1B9A;
}

.voice-btn {
    width: 100%;
    background: linear-gradient(135deg, #673AB7, #9C27B0);
    border: none;
    color: var(--white);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.voice-btn:hover {
    background: linear-gradient(135deg, #5E35B1, #8E24AA);
    transform: translateY(-1px);
}

/* Main Chat Area */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--white);
    position: relative;
}

/* Chat Header */
.chat-header {
    padding: 20px 30px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--white);
    position: sticky;
    top: 0;
    z-index: 10;
}

.chat-header-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bot-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    position: relative;
}

.status-indicator {
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border: 2px solid var(--white);
    border-radius: 50%;
    position: absolute;
    bottom: -2px;
    right: -2px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.bot-info h2 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 2px;
    color: var(--text-dark);
}

.bot-status {
    font-size: 0.85rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 6px;
}

.bot-status i {
    color: #4CAF50;
    font-size: 0.6rem;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.action-icon {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-light);
}

.action-icon:hover {
    background: var(--gray-200);
    color: var(--text-dark);
}

/* Welcome Card */
.welcome-card {
    margin: 30px;
    padding: 40px;
    background: linear-gradient(135deg, var(--light-orange) 0%, rgba(255, 215, 0, 0.1) 100%);
    border-radius: var(--border-radius-large);
    border: 1px solid rgba(255, 107, 53, 0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.05) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.welcome-content {
    position: relative;
    z-index: 1;
}

.welcome-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 1.5rem;
}

.welcome-content h2 {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    color: var(--dark-red);
    margin-bottom: 15px;
    font-weight: 600;
}

.welcome-content p {
    color: var(--text-light);
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.welcome-suggestions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 30px;
}

.suggestion-card {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    text-align: center;
}

.suggestion-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.suggestion-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.suggestion-card span {
    font-weight: 500;
    color: var(--text-dark);
    font-size: 0.9rem;
}

/* Chat Messages Container */
.chat-messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 30px;
    scroll-behavior: smooth;
}

.chat-messages {
    padding: 20px 0;
    min-height: 100%;
}

.message {
    margin-bottom: 25px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
    position: relative;
}

.message.user .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
}

.message.bot .message-avatar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
}

.message-content {
    max-width: 75%;
    padding: 16px 20px;
    border-radius: 18px;
    line-height: 1.6;
    word-wrap: break-word;
    position: relative;
    font-size: 0.95rem;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
    border-bottom-right-radius: 6px;
}

.message.bot .message-content {
    background: var(--gray-100);
    color: var(--text-dark);
    border-bottom-left-radius: 6px;
    border: 1px solid var(--gray-200);
}

.message-content strong {
    font-weight: 600;
}

.message-content em {
    font-style: italic;
    color: inherit;
}

.message-content a {
    color: inherit;
    text-decoration: underline;
}

.message.user .message-content a {
    color: rgba(255, 255, 255, 0.9);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 5px;
    text-align: right;
}

.message.user .message-time {
    text-align: left;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 20px 0;
    animation: messageSlideIn 0.3s ease-out;
}

.typing-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.typing-content {
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: 18px;
    border-bottom-left-radius: 6px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }
.typing-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.typing-text {
    font-size: 0.9rem;
    color: var(--text-light);
    font-style: italic;
}

/* Chat Input Area */
.chat-input-area {
    padding: 20px 30px 30px;
    background: var(--white);
    border-top: 1px solid var(--gray-200);
}

.input-container {
    margin-bottom: 15px;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: var(--gray-100);
    border: 2px solid transparent;
    border-radius: var(--border-radius-large);
    padding: 12px 16px;
    transition: var(--transition);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.attachment-btn {
    width: 35px;
    height: 35px;
    border: none;
    background: var(--gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-light);
    flex-shrink: 0;
}

.attachment-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Image Preview Container */
.image-preview-container {
    margin-bottom: 15px;
    padding: 15px;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    border: 2px dashed var(--gray-300);
}

.image-preview {
    display: flex;
    align-items: center;
    gap: 15px;
}

.image-preview img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid var(--white);
    box-shadow: var(--shadow-light);
}

.image-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-name {
    font-size: 0.9rem;
    color: var(--text-dark);
    font-weight: 500;
}

.remove-image {
    width: 30px;
    height: 30px;
    border: none;
    background: #f44336;
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-image:hover {
    background: #d32f2f;
    transform: scale(1.1);
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 1rem;
    line-height: 1.5;
    resize: none;
    outline: none;
    font-family: inherit;
    color: var(--text-dark);
    min-height: 24px;
    max-height: 120px;
}

#messageInput::placeholder {
    color: var(--text-light);
}

.input-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.char-count {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

#sendButton {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

#sendButton:hover:not(:disabled) {
    background: var(--secondary-color);
    transform: scale(1.05);
}

#sendButton:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
}

/* Quick Replies */
.quick-replies {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-reply {
    background: var(--white);
    border: 1px solid var(--gray-200);
    color: var(--text-dark);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    font-weight: 500;
}

.quick-reply:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Floating Action Menu */
.floating-menu {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 100;
}

.fab-main {
    width: 60px;
    height: 60px;
    border: none;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-medium);
    position: relative;
    z-index: 101;
}

.fab-main:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-heavy);
}

.fab-options {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition);
}

.floating-menu.active .fab-options {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-option {
    width: 45px;
    height: 45px;
    border: none;
    background: var(--white);
    color: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--gray-200);
}

.fab-option:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: var(--white);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.loading-content p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Image Upload Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-heavy);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #4CAF50, #66BB6A);
    color: var(--white);
}

.modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    width: 35px;
    height: 35px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body {
    padding: 25px;
}

.upload-area {
    border: 3px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    margin-bottom: 25px;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.05);
}

.upload-content i {
    font-size: 3rem;
    color: #4CAF50;
    margin-bottom: 15px;
}

.upload-content h4 {
    font-size: 1.1rem;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-weight: 600;
}

.upload-content p {
    color: var(--text-light);
    margin-bottom: 5px;
}

.upload-note {
    font-size: 0.8rem !important;
    color: var(--text-light) !important;
    margin-bottom: 20px !important;
}

.upload-btn {
    background: #4CAF50;
    color: var(--white);
    border: none;
    padding: 12px 25px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.upload-btn:hover {
    background: #388E3C;
    transform: translateY(-1px);
}

.analysis-options {
    margin-top: 20px;
}

.analysis-options h4 {
    font-size: 1rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 600;
}

.option-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.option-btn {
    flex: 1;
    min-width: 120px;
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    background: var(--white);
    color: var(--text-dark);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    font-weight: 500;
}

.option-btn:hover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.05);
}

.option-btn.active {
    border-color: #4CAF50;
    background: #4CAF50;
    color: var(--white);
}

.option-btn i {
    font-size: 1.2rem;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: var(--gray-100);
}

.btn-secondary {
    padding: 10px 20px;
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--text-dark);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.btn-secondary:hover {
    background: var(--gray-100);
}

.btn-primary {
    padding: 10px 20px;
    border: none;
    background: #4CAF50;
    color: var(--white);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover:not(:disabled) {
    background: #388E3C;
    transform: translateY(-1px);
}

.btn-primary:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
}

/* Heritage Sites Modal */
.heritage-modal {
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

.heritage-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid var(--gray-200);
    background: var(--white);
    color: var(--text-dark);
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    background: rgba(255, 107, 53, 0.05);
}

.filter-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.heritage-sites-list {
    display: grid;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.heritage-site-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.heritage-site-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.heritage-site-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.heritage-site-title h4 {
    font-size: 1.1rem;
    color: var(--text-dark);
    margin-bottom: 5px;
    font-weight: 600;
}

.heritage-site-title .tamil-name {
    font-size: 0.9rem;
    color: var(--text-light);
    font-style: italic;
}

.heritage-site-category {
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.heritage-site-info {
    margin-bottom: 15px;
}

.heritage-site-info p {
    color: var(--text-light);
    line-height: 1.5;
    margin-bottom: 10px;
}

.heritage-site-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    font-size: 0.85rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-light);
}

.detail-item i {
    color: var(--primary-color);
    width: 16px;
}

.heritage-site-distance {
    background: #E8F5E8;
    color: #2E7D32;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Voice Modal */
.voice-modal {
    max-width: 500px;
}

.voice-controls {
    text-align: center;
}

.language-selector {
    margin-bottom: 25px;
}

.language-selector label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-dark);
}

.language-selector select {
    padding: 10px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--white);
    color: var(--text-dark);
    cursor: pointer;
    transition: var(--transition);
}

.language-selector select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.voice-status {
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.voice-status i {
    font-size: 3rem;
    color: var(--gray-300);
}

.voice-status.recording i {
    color: #f44336;
    animation: pulse 1s infinite;
}

.voice-status.processing i {
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

.voice-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 25px;
}

.voice-record-btn,
.voice-stop-btn {
    padding: 15px 25px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    font-size: 1rem;
}

.voice-record-btn {
    background: #4CAF50;
    color: var(--white);
}

.voice-record-btn:hover {
    background: #388E3C;
    transform: translateY(-2px);
}

.voice-stop-btn {
    background: #f44336;
    color: var(--white);
}

.voice-stop-btn:hover {
    background: #d32f2f;
    transform: translateY(-2px);
}

.voice-transcript {
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: left;
}

.voice-transcript h4 {
    margin-bottom: 10px;
    color: var(--text-dark);
}

.voice-transcript p {
    background: var(--white);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--gray-200);
    margin-bottom: 15px;
    line-height: 1.5;
}

.send-transcript-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.send-transcript-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .app-container {
        margin: 10px;
        border-radius: var(--border-radius);
    }

    .sidebar {
        width: 280px;
    }

    .welcome-suggestions {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    body {
        background: var(--white);
    }

    .app-container {
        margin: 0;
        border-radius: 0;
        height: 100vh;
    }

    .sidebar {
        position: fixed;
        left: -320px;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: var(--transition);
        width: 280px;
    }

    .sidebar.active {
        left: 0;
    }

    .chat-main {
        width: 100%;
    }

    .chat-header {
        padding: 15px 20px;
    }

    .welcome-card {
        margin: 20px;
        padding: 30px 20px;
    }

    .welcome-content h2 {
        font-size: 1.5rem;
    }

    .welcome-suggestions {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .chat-messages-container {
        padding: 0 20px;
    }

    .message-content {
        max-width: 85%;
        font-size: 0.9rem;
    }

    .chat-input-area {
        padding: 15px 20px 20px;
    }

    .floating-menu {
        bottom: 20px;
        right: 20px;
    }

    .fab-main {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .fab-option {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .chat-header-info {
        gap: 10px;
    }

    .bot-avatar {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .bot-info h2 {
        font-size: 1rem;
    }

    .bot-status {
        font-size: 0.8rem;
    }

    .welcome-card {
        margin: 15px;
        padding: 25px 15px;
    }

    .welcome-content h2 {
        font-size: 1.3rem;
    }

    .welcome-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .quick-replies {
        justify-content: center;
    }

    /* Modal responsive adjustments */
    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px 20px;
    }

    .upload-area {
        padding: 30px 15px;
    }

    .upload-content i {
        font-size: 2.5rem;
    }

    .option-buttons {
        flex-direction: column;
    }

    .option-btn {
        min-width: auto;
    }

    .modal-footer {
        flex-direction: column;
    }

    .btn-secondary,
    .btn-primary {
        width: 100%;
        justify-content: center;
    }
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Scrollbar Styling */
.chat-messages-container::-webkit-scrollbar {
    width: 6px;
}

.chat-messages-container::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Focus States */
.action-btn:focus,
.suggestion-card:focus,
.quick-reply:focus,
.fab-main:focus,
.fab-option:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Animation for better UX */
.message-content,
.suggestion-card,
.quick-reply,
.action-btn {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Print Styles */
@media print {
    .sidebar,
    .chat-input-area,
    .floating-menu,
    .loading-overlay {
        display: none !important;
    }

    .chat-main {
        width: 100% !important;
    }

    .message-content {
        max-width: 100% !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

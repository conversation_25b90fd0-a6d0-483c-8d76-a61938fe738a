// DOM Elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const chatMessages = document.getElementById('chatMessages');
const typingIndicator = document.getElementById('typingIndicator');
const charCount = document.getElementById('charCount');
const welcomeCard = document.getElementById('welcomeCard');
const loadingOverlay = document.getElementById('loadingOverlay');
const clearChatBtn = document.getElementById('clearChat');
const toggleSidebarBtn = document.getElementById('toggleSidebar');
const fabMain = document.getElementById('fabMain');
const floatingMenu = document.getElementById('floatingMenu');
const quickReplies = document.getElementById('quickReplies');

// Image upload elements
const attachmentBtn = document.getElementById('attachmentBtn');
const imageInput = document.getElementById('imageInput');
const imagePreviewContainer = document.getElementById('imagePreviewContainer');
const previewImage = document.getElementById('previewImage');
const imageName = document.getElementById('imageName');
const removeImageBtn = document.getElementById('removeImage');

// Modal elements
const imageUploadModal = document.getElementById('imageUploadModal');
const openImageUploadBtn = document.getElementById('openImageUpload');
const closeImageModalBtn = document.getElementById('closeImageModal');
const modalImageInput = document.getElementById('modalImageInput');
const selectImageBtn = document.getElementById('selectImageBtn');
const uploadArea = document.getElementById('uploadArea');
const analyzeImageBtn = document.getElementById('analyzeImage');
const cancelUploadBtn = document.getElementById('cancelUpload');

// Heritage and forest elements
const heritageSitesModal = document.getElementById('heritageSitesModal');
const findHeritageSitesBtn = document.getElementById('findHeritageSites');
const closeHeritageModalBtn = document.getElementById('closeHeritageModal');
const heritageSitesList = document.getElementById('heritageSitesList');
const locationBtn = document.getElementById('locationBtn');
const openForestAnalysisBtn = document.getElementById('openForestAnalysis');

// Voice elements
const voiceModal = document.getElementById('voiceModal');
const voiceToggleBtn = document.getElementById('voiceToggle');
const startVoiceInputBtn = document.getElementById('startVoiceInput');
const closeVoiceModalBtn = document.getElementById('closeVoiceModal');
const voiceLanguageSelect = document.getElementById('voiceLanguage');
const voiceStatus = document.getElementById('voiceStatus');
const startRecordingBtn = document.getElementById('startRecording');
const stopRecordingBtn = document.getElementById('stopRecording');
const voiceTranscript = document.getElementById('voiceTranscript');
const transcriptText = document.getElementById('transcriptText');
const sendTranscriptBtn = document.getElementById('sendTranscript');

// State
let isTyping = false;
let messageHistory = [];
let sidebarOpen = false;
let selectedImage = null;
let analysisType = 'full';
let userLocation = null;
let isRecording = false;
let mediaRecorder = null;
let audioChunks = [];
let recognition = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    addWelcomeMessage();
    autoResizeTextarea();
    initializeVoiceRecognition();
    requestLocationPermission();
    messageInput.focus();
});

// Event Listeners
function initializeEventListeners() {
    // Send button click
    sendButton.addEventListener('click', handleSendMessage);

    // Enter key press (Ctrl/Cmd + Enter to send, Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            handleSendMessage();
        }
    });

    // Character count and auto-resize
    messageInput.addEventListener('input', function() {
        updateCharCount();
        autoResizeTextarea();
        updateSendButton();
    });

    // Welcome card suggestions
    if (welcomeCard) {
        welcomeCard.addEventListener('click', function(e) {
            if (e.target.closest('.suggestion-card')) {
                const message = e.target.closest('.suggestion-card').getAttribute('data-message');
                messageInput.value = message;
                handleSendMessage();
            }
        });
    }

    // Quick replies
    if (quickReplies) {
        quickReplies.addEventListener('click', function(e) {
            if (e.target.classList.contains('quick-reply')) {
                const message = e.target.getAttribute('data-message');
                messageInput.value = message;
                handleSendMessage();
            }
        });
    }

    // Sidebar action buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.action-btn')) {
            const topic = e.target.closest('.action-btn').getAttribute('data-topic');
            handleTopicClick(topic);
        }
    });

    // Clear chat
    if (clearChatBtn) {
        clearChatBtn.addEventListener('click', clearChat);
    }

    // Toggle sidebar (mobile)
    if (toggleSidebarBtn) {
        toggleSidebarBtn.addEventListener('click', toggleSidebar);
    }

    // Floating action button
    if (fabMain) {
        fabMain.addEventListener('click', toggleFloatingMenu);
    }

    // FAB options
    document.addEventListener('click', function(e) {
        if (e.target.closest('.fab-option')) {
            const action = e.target.closest('.fab-option').getAttribute('data-action');
            handleFabAction(action);
        }
    });

    // Close floating menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.floating-menu')) {
            closeFloatingMenu();
        }
    });

    // Mobile sidebar overlay
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('sidebar-overlay')) {
            closeSidebar();
        }
    });

    // Image upload event listeners
    initializeImageUpload();

    // Heritage sites event listeners
    initializeHeritageFeatures();

    // Voice assistant event listeners
    initializeVoiceFeatures();
}

// Initialize image upload functionality
function initializeImageUpload() {
    // Attachment button click
    if (attachmentBtn) {
        attachmentBtn.addEventListener('click', function() {
            imageInput.click();
        });
    }

    // Image input change
    if (imageInput) {
        imageInput.addEventListener('change', handleImageSelect);
    }

    // Remove image button
    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', removeSelectedImage);
    }

    // Open image upload modal
    if (openImageUploadBtn) {
        openImageUploadBtn.addEventListener('click', openImageModal);
    }

    // Close modal
    if (closeImageModalBtn) {
        closeImageModalBtn.addEventListener('click', closeImageModal);
    }

    if (cancelUploadBtn) {
        cancelUploadBtn.addEventListener('click', closeImageModal);
    }

    // Modal image input
    if (modalImageInput) {
        modalImageInput.addEventListener('change', handleModalImageSelect);
    }

    // Select image button in modal
    if (selectImageBtn) {
        selectImageBtn.addEventListener('click', function() {
            modalImageInput.click();
        });
    }

    // Analyze image button
    if (analyzeImageBtn) {
        analyzeImageBtn.addEventListener('click', handleImageAnalysis);
    }

    // Analysis type buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.option-btn')) {
            const btn = e.target.closest('.option-btn');
            document.querySelectorAll('.option-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            analysisType = btn.getAttribute('data-type');
        }
    });

    // Drag and drop functionality
    if (uploadArea) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
    }

    // Close modal when clicking outside
    if (imageUploadModal) {
        imageUploadModal.addEventListener('click', function(e) {
            if (e.target === imageUploadModal) {
                closeImageModal();
            }
        });
    }
}

// Add welcome message
function addWelcomeMessage() {
    const welcomeText = "வணக்கம்! (Vanakkam!) Welcome to Tamil Nadu! I'm your AI travel companion, ready to help you explore this incredible state. Feel free to ask me anything about temples, food, culture, transportation, or any other aspect of Tamil Nadu travel!";
    addMessage(welcomeText, 'bot');
}

// Initialize heritage features
function initializeHeritageFeatures() {
    // Heritage sites button
    if (findHeritageSitesBtn) {
        findHeritageSitesBtn.addEventListener('click', openHeritageSitesModal);
    }

    // Location button in header
    if (locationBtn) {
        locationBtn.addEventListener('click', openHeritageSitesModal);
    }

    // Close heritage modal
    if (closeHeritageModalBtn) {
        closeHeritageModalBtn.addEventListener('click', closeHeritageSitesModal);
    }

    // Heritage filter buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('filter-btn')) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            e.target.classList.add('active');
            const category = e.target.getAttribute('data-category');
            filterHeritageSites(category);
        }
    });

    // Forest analysis button
    if (openForestAnalysisBtn) {
        openForestAnalysisBtn.addEventListener('click', function() {
            analysisType = 'forest';
            openImageModal();
        });
    }
}

// Initialize voice features
function initializeVoiceFeatures() {
    // Voice toggle button
    if (voiceToggleBtn) {
        voiceToggleBtn.addEventListener('click', openVoiceModal);
    }

    // Start voice input button
    if (startVoiceInputBtn) {
        startVoiceInputBtn.addEventListener('click', openVoiceModal);
    }

    // Close voice modal
    if (closeVoiceModalBtn) {
        closeVoiceModalBtn.addEventListener('click', closeVoiceModal);
    }

    // Recording buttons
    if (startRecordingBtn) {
        startRecordingBtn.addEventListener('click', startVoiceRecording);
    }

    if (stopRecordingBtn) {
        stopRecordingBtn.addEventListener('click', stopVoiceRecording);
    }

    // Send transcript button
    if (sendTranscriptBtn) {
        sendTranscriptBtn.addEventListener('click', sendVoiceTranscript);
    }
}

// Handle topic clicks from sidebar
function handleTopicClick(topic) {
    const topicMessages = {
        temples: "Tell me about the most famous temples in Tamil Nadu and their significance",
        food: "What are the must-try Tamil Nadu dishes and where can I find authentic local food?",
        transport: "How do I travel around Tamil Nadu? What are the best transportation options?",
        culture: "Tell me about Tamil Nadu's culture, traditions, and festivals",
        places: "What are the top tourist destinations and hidden gems in Tamil Nadu?",
        shopping: "Where can I shop for traditional Tamil items like sarees, handicrafts, and souvenirs?",
        agriculture: "Tell me about Tamil Nadu's agriculture, major crops, and farming practices",
        crops: "What are the main crops grown in Tamil Nadu and their growing seasons?",
        heritage: "Show me the most important heritage sites and monuments in Tamil Nadu",
        forest: "Tell me about the medicinal plants and forest biodiversity in Tamil Nadu"
    };

    if (topicMessages[topic]) {
        messageInput.value = topicMessages[topic];
        handleSendMessage();
    }
}

// Image handling functions
function handleImageSelect(event) {
    const file = event.target.files[0];
    if (file) {
        displayImagePreview(file);
    }
}

function handleModalImageSelect(event) {
    const file = event.target.files[0];
    if (file) {
        selectedImage = file;
        analyzeImageBtn.disabled = false;

        // Update upload area to show selected file
        const uploadContent = uploadArea.querySelector('.upload-content');
        uploadContent.innerHTML = `
            <i class="fas fa-check-circle" style="color: #4CAF50;"></i>
            <h4>Image Selected</h4>
            <p>${file.name}</p>
            <p class="upload-note">Ready for analysis</p>
        `;
    }
}

function displayImagePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImage.src = e.target.result;
        imageName.textContent = file.name;
        imagePreviewContainer.style.display = 'block';
        selectedImage = file;

        // Update attachment button to show image is selected
        attachmentBtn.innerHTML = '<i class="fas fa-check"></i>';
        attachmentBtn.style.background = '#4CAF50';
    };
    reader.readAsDataURL(file);
}

function removeSelectedImage() {
    selectedImage = null;
    imagePreviewContainer.style.display = 'none';
    imageInput.value = '';

    // Reset attachment button
    attachmentBtn.innerHTML = '<i class="fas fa-camera"></i>';
    attachmentBtn.style.background = '';
}

function openImageModal() {
    imageUploadModal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    imageUploadModal.style.display = 'none';
    document.body.style.overflow = '';

    // Reset modal state
    selectedImage = null;
    analyzeImageBtn.disabled = true;
    modalImageInput.value = '';

    // Reset upload area
    const uploadContent = uploadArea.querySelector('.upload-content');
    uploadContent.innerHTML = `
        <i class="fas fa-cloud-upload-alt"></i>
        <h4>Upload Plant/Crop Image</h4>
        <p>Drag and drop an image here, or click to select</p>
        <p class="upload-note">Supported formats: JPG, PNG, GIF, WebP (Max 10MB)</p>
        <button class="upload-btn" id="selectImageBtn">
            <i class="fas fa-folder-open"></i>
            Select Image
        </button>
    `;

    // Re-attach event listener
    const newSelectBtn = uploadContent.querySelector('#selectImageBtn');
    if (newSelectBtn) {
        newSelectBtn.addEventListener('click', function() {
            modalImageInput.click();
        });
    }
}

// Handle floating action button actions
function handleFabAction(action) {
    const fabMessages = {
        weather: "What's the current weather like in Tamil Nadu and what's the best time to visit?",
        emergency: "What are the important emergency contact numbers and safety tips for tourists in Tamil Nadu?",
        translate: "Can you teach me some basic Tamil phrases that would be helpful for tourists?"
    };

    if (fabMessages[action]) {
        messageInput.value = fabMessages[action];
        handleSendMessage();
    }
    closeFloatingMenu();
}

// Toggle floating menu
function toggleFloatingMenu() {
    floatingMenu.classList.toggle('active');
}

// Close floating menu
function closeFloatingMenu() {
    floatingMenu.classList.remove('active');
}

// Toggle sidebar (mobile)
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay') || createSidebarOverlay();

    sidebarOpen = !sidebarOpen;
    sidebar.classList.toggle('active', sidebarOpen);
    overlay.classList.toggle('active', sidebarOpen);
}

// Close sidebar
function closeSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    sidebarOpen = false;
    sidebar.classList.remove('active');
    if (overlay) overlay.classList.remove('active');
}

// Create sidebar overlay for mobile
function createSidebarOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'sidebar-overlay';
    document.body.appendChild(overlay);
    return overlay;
}

// Clear chat
function clearChat() {
    if (confirm('Are you sure you want to clear the chat history?')) {
        chatMessages.innerHTML = '';
        messageHistory = [];
        if (welcomeCard) welcomeCard.style.display = 'block';
        if (quickReplies) quickReplies.style.display = 'flex';
        addWelcomeMessage();
    }
}

// Heritage Sites Functions
function openHeritageSitesModal() {
    if (heritageSitesModal) {
        heritageSitesModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        loadHeritageSites();
    }
}

function closeHeritageSitesModal() {
    if (heritageSitesModal) {
        heritageSitesModal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

async function loadHeritageSites(category = 'all') {
    try {
        let url = '/heritage-sites';
        const params = new URLSearchParams();

        if (category !== 'all') {
            params.append('category', category);
        }

        if (userLocation) {
            params.append('lat', userLocation.latitude);
            params.append('lng', userLocation.longitude);
            params.append('radius', 100); // 100km radius
        }

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await fetch(url);
        const data = await response.json();

        if (data.status === 'success') {
            displayHeritageSites(data.sites);
        } else {
            console.error('Error loading heritage sites:', data.error);
        }

    } catch (error) {
        console.error('Error loading heritage sites:', error);
    }
}

function displayHeritageSites(sites) {
    if (!heritageSitesList) return;

    heritageSitesList.innerHTML = '';

    if (sites.length === 0) {
        heritageSitesList.innerHTML = '<p>No heritage sites found for the selected criteria.</p>';
        return;
    }

    sites.forEach(site => {
        const siteCard = document.createElement('div');
        siteCard.className = 'heritage-site-card';
        siteCard.innerHTML = `
            <div class="heritage-site-header">
                <div class="heritage-site-title">
                    <h4>${site.name}</h4>
                    <div class="tamil-name">${site.name_tamil || ''}</div>
                </div>
                <div class="heritage-site-category">${site.category}</div>
            </div>
            <div class="heritage-site-info">
                <p>${site.description}</p>
            </div>
            <div class="heritage-site-details">
                <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${site.district}</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-clock"></i>
                    <span>${site.visiting_hours}</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-ticket-alt"></i>
                    <span>${site.entry_fee}</span>
                </div>
                ${site.distance ? `<div class="heritage-site-distance">${site.distance} km away</div>` : ''}
            </div>
        `;

        siteCard.addEventListener('click', () => {
            closeHeritageSitesModal();
            const message = `Tell me more about ${site.name} in ${site.district}. I'd like to know about its history, architecture, and visiting tips.`;
            messageInput.value = message;
            handleSendMessage();
        });

        heritageSitesList.appendChild(siteCard);
    });
}

function filterHeritageSites(category) {
    loadHeritageSites(category);
}

// Location Functions
function requestLocationPermission() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                userLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };
                console.log('Location obtained:', userLocation);
            },
            (error) => {
                console.log('Location permission denied or unavailable:', error);
            }
        );
    }
}

// Drag and drop handlers
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            selectedImage = file;
            analyzeImageBtn.disabled = false;

            // Update upload area
            const uploadContent = uploadArea.querySelector('.upload-content');
            uploadContent.innerHTML = `
                <i class="fas fa-check-circle" style="color: #4CAF50;"></i>
                <h4>Image Dropped Successfully</h4>
                <p>${file.name}</p>
                <p class="upload-note">Ready for analysis</p>
            `;
        }
    }
}

// Handle image analysis
async function handleImageAnalysis() {
    if (!selectedImage) return;

    const formData = new FormData();
    formData.append('image', selectedImage);

    // Close modal and show loading
    closeImageModal();
    showTypingIndicator();

    // Hide welcome elements
    if (welcomeCard) welcomeCard.style.display = 'none';
    if (quickReplies) quickReplies.style.display = 'none';

    try {
        const response = await fetch('/analyze-image', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.status === 'success') {
            // Add image message first
            addImageMessage(selectedImage, 'user');

            // Add analysis result
            addMessage(data.analysis, 'bot');
            messageHistory.push({role: 'assistant', content: data.analysis});
        } else {
            throw new Error(data.error || 'Failed to analyze image');
        }

    } catch (error) {
        console.error('Error:', error);
        addMessage('Sorry, I encountered an error while analyzing the image. Please try again.', 'bot', true);
    } finally {
        hideTypingIndicator();
        selectedImage = null;
    }
}

// Handle sending messages (updated to support images)
async function handleSendMessage() {
    const message = messageInput.value.trim();

    if ((!message && !selectedImage) || isTyping) return;

    // Hide welcome card and quick replies after first message
    if (messageHistory.length === 0) {
        if (welcomeCard) welcomeCard.style.display = 'none';
        if (quickReplies) quickReplies.style.display = 'none';
    }

    // Show typing indicator
    showTypingIndicator();

    try {
        if (selectedImage) {
            // Send message with image
            const formData = new FormData();
            formData.append('image', selectedImage);
            formData.append('message', message || 'Please analyze this agricultural image');

            // Add user message with image
            addImageMessage(selectedImage, 'user', message);

            const response = await fetch('/chat-with-image', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'success') {
                addMessage(data.response, 'bot');
                messageHistory.push({role: 'assistant', content: data.response});
            } else {
                throw new Error(data.error || 'Failed to process image');
            }

            // Clear image
            removeSelectedImage();

        } else {
            // Regular text message
            addMessage(message, 'user');
            messageHistory.push({role: 'user', content: message});

            const response = await fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                addMessage(data.response, 'bot');
                messageHistory.push({role: 'assistant', content: data.response});
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        }

    } catch (error) {
        console.error('Error:', error);
        const errorMessage = error.message.includes('API key')
            ? 'Please configure your OpenAI API key in the .env file to use the chatbot.'
            : 'Sorry, I encountered an error. Please check your internet connection and try again.';
        addMessage(errorMessage, 'bot', true);
    } finally {
        // Clear input and reset
        messageInput.value = '';
        updateCharCount();
        autoResizeTextarea();
        updateSendButton();
        hideTypingIndicator();
        messageInput.focus();
    }
}

// Add message to chat
function addMessage(content, sender, isError = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    if (isError) {
        messageContent.style.background = '#ffebee';
        messageContent.style.borderColor = '#f44336';
        messageContent.style.color = '#c62828';
    }

    // Format message content
    messageContent.innerHTML = formatMessage(content);

    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';
    messageTime.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    messageDiv.appendChild(avatar);
    const contentWrapper = document.createElement('div');
    contentWrapper.appendChild(messageContent);
    contentWrapper.appendChild(messageTime);
    messageDiv.appendChild(contentWrapper);

    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// Add image message to chat
function addImageMessage(imageFile, sender, text = '') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content image-message';

    // Create image element
    const img = document.createElement('img');
    img.className = 'message-image';
    img.style.maxWidth = '200px';
    img.style.maxHeight = '200px';
    img.style.borderRadius = '8px';
    img.style.marginBottom = text ? '10px' : '0';

    // Create image URL
    const reader = new FileReader();
    reader.onload = function(e) {
        img.src = e.target.result;
    };
    reader.readAsDataURL(imageFile);

    messageContent.appendChild(img);

    // Add text if provided
    if (text) {
        const textDiv = document.createElement('div');
        textDiv.innerHTML = formatMessage(text);
        messageContent.appendChild(textDiv);
    }

    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';
    messageTime.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    messageDiv.appendChild(avatar);
    const contentWrapper = document.createElement('div');
    contentWrapper.appendChild(messageContent);
    contentWrapper.appendChild(messageTime);
    messageDiv.appendChild(contentWrapper);

    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// Format message content
function formatMessage(content) {
    // Convert line breaks to <br>
    content = content.replace(/\n/g, '<br>');
    
    // Make text in **bold** actually bold
    content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Make text in *italic* actually italic
    content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // Convert URLs to clickable links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    content = content.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
    
    return content;
}

// Show typing indicator
function showTypingIndicator() {
    isTyping = true;
    typingIndicator.style.display = 'flex';
    sendButton.disabled = true;
    scrollToBottom();
}

// Hide typing indicator
function hideTypingIndicator() {
    isTyping = false;
    typingIndicator.style.display = 'none';
    sendButton.disabled = false;
}

// Update character count
function updateCharCount() {
    const count = messageInput.value.length;
    if (charCount) {
        charCount.textContent = `${count}/500`;

        if (count > 450) {
            charCount.style.color = '#f44336';
        } else if (count > 400) {
            charCount.style.color = '#ff9800';
        } else {
            charCount.style.color = '#666';
        }
    }
}

// Auto-resize textarea
function autoResizeTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
}

// Update send button state
function updateSendButton() {
    const hasText = messageInput.value.trim().length > 0;
    sendButton.disabled = !hasText || isTyping;
}

// Scroll to bottom of chat
function scrollToBottom() {
    setTimeout(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }, 100);
}

// Show loading overlay
function showLoading() {
    loadingOverlay.style.display = 'flex';
}

// Hide loading overlay
function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// Error handling for network issues
window.addEventListener('online', function() {
    console.log('Connection restored');
});

window.addEventListener('offline', function() {
    addMessage('You appear to be offline. Please check your internet connection.', 'bot', true);
});

// Prevent form submission on enter in input
messageInput.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.shiftKey) {
        // Allow Shift+Enter for new lines
        return;
    }
});

// Focus input when clicking anywhere in the chat area
chatMessages.addEventListener('click', function() {
    messageInput.focus();
});

// Add some helpful keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + / to focus input
    if ((e.ctrlKey || e.metaKey) && e.key === '/') {
        e.preventDefault();
        messageInput.focus();
    }

    // Escape to clear input or close menus
    if (e.key === 'Escape') {
        if (floatingMenu.classList.contains('active')) {
            closeFloatingMenu();
        } else if (sidebarOpen) {
            closeSidebar();
        } else {
            messageInput.value = '';
            updateCharCount();
            autoResizeTextarea();
            updateSendButton();
            messageInput.focus();
        }
    }

    // Ctrl/Cmd + K to clear chat
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        clearChat();
    }
});

// Initialize mobile responsiveness
function initializeMobileFeatures() {
    // Create sidebar overlay if on mobile
    if (window.innerWidth <= 768) {
        createSidebarOverlay();
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            closeSidebar();
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) overlay.remove();
        } else if (!document.querySelector('.sidebar-overlay')) {
            createSidebarOverlay();
        }
    });
}

// Initialize mobile features
initializeMobileFeatures();

// Add smooth scrolling behavior
function smoothScrollToBottom() {
    const container = document.querySelector('.chat-messages-container');
    if (container) {
        container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
        });
    }
}

// Enhanced scroll to bottom
function scrollToBottom() {
    setTimeout(smoothScrollToBottom, 100);
}

// Add loading states for better UX
function setLoadingState(loading) {
    if (loading) {
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        sendButton.disabled = true;
    } else {
        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
        updateSendButton();
    }
}

// Voice Recognition Functions
function initializeVoiceRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;

        recognition.onresult = function(event) {
            const transcript = event.results[0][0].transcript;
            if (transcriptText) {
                transcriptText.textContent = transcript;
                voiceTranscript.style.display = 'block';
            }
            updateVoiceStatus('Recognized text successfully', 'success');
        };

        recognition.onerror = function(event) {
            console.error('Speech recognition error:', event.error);
            updateVoiceStatus('Error recognizing speech. Please try again.', 'error');
            stopVoiceRecording();
        };

        recognition.onend = function() {
            stopVoiceRecording();
        };
    }
}

function openVoiceModal() {
    if (voiceModal) {
        voiceModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        resetVoiceModal();
    }
}

function closeVoiceModal() {
    if (voiceModal) {
        voiceModal.style.display = 'none';
        document.body.style.overflow = '';
        if (isRecording) {
            stopVoiceRecording();
        }
    }
}

function resetVoiceModal() {
    if (voiceTranscript) {
        voiceTranscript.style.display = 'none';
    }
    updateVoiceStatus('Click to start voice input', 'idle');
}

function startVoiceRecording() {
    if (!recognition) {
        alert('Speech recognition is not supported in your browser.');
        return;
    }

    isRecording = true;
    const language = voiceLanguageSelect ? voiceLanguageSelect.value : 'en';

    // Language mapping for speech recognition
    const langMap = {
        'en': 'en-US',
        'ta': 'ta-IN',
        'hi': 'hi-IN'
    };

    recognition.lang = langMap[language] || 'en-US';

    updateVoiceStatus('Listening... Speak now', 'recording');

    if (startRecordingBtn) startRecordingBtn.style.display = 'none';
    if (stopRecordingBtn) stopRecordingBtn.style.display = 'flex';

    try {
        recognition.start();
    } catch (error) {
        console.error('Error starting recognition:', error);
        stopVoiceRecording();
    }
}

function stopVoiceRecording() {
    isRecording = false;

    if (recognition) {
        recognition.stop();
    }

    updateVoiceStatus('Processing...', 'processing');

    if (startRecordingBtn) startRecordingBtn.style.display = 'flex';
    if (stopRecordingBtn) stopRecordingBtn.style.display = 'none';
}

function updateVoiceStatus(message, status) {
    if (!voiceStatus) return;

    const statusIcon = voiceStatus.querySelector('i');
    const statusText = voiceStatus.querySelector('span');

    if (statusText) statusText.textContent = message;

    // Remove all status classes
    voiceStatus.classList.remove('recording', 'processing', 'success', 'error');

    // Add appropriate status class
    if (status) {
        voiceStatus.classList.add(status);
    }

    // Update icon based on status
    if (statusIcon) {
        statusIcon.className = 'fas ';
        switch (status) {
            case 'recording':
                statusIcon.className += 'fa-microphone';
                break;
            case 'processing':
                statusIcon.className += 'fa-spinner fa-spin';
                break;
            case 'success':
                statusIcon.className += 'fa-check-circle';
                break;
            case 'error':
                statusIcon.className += 'fa-exclamation-triangle';
                break;
            default:
                statusIcon.className += 'fa-microphone-slash';
        }
    }
}

function sendVoiceTranscript() {
    if (transcriptText && transcriptText.textContent) {
        messageInput.value = transcriptText.textContent;
        closeVoiceModal();
        handleSendMessage();
    }
}

from flask import Flask, request, jsonify, render_template, send_file
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from openai import OpenAI
import os
import base64
import io
import json
import requests
from datetime import datetime, timedelta
from PIL import Image
from dotenv import load_dotenv
from geopy.distance import geodesic
from geopy.geocoders import Nominatim
import speech_recognition as sr
from gtts import gTTS
import tempfile
from heritage_data import HERITAGE_SITES_DATA, MEDICINAL_PLANTS_DATA

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Configure database
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///vaanga_suthalam.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# Configure OpenAI
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Initialize geocoder
geolocator = Nominatim(user_agent="vaanga_suthalam")

# Database Models
class HeritageSite(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    name_tamil = db.Column(db.String(200))
    category = db.Column(db.String(100))  # temple, palace, monument, fort
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    district = db.Column(db.String(100))
    description = db.Column(db.Text)
    historical_significance = db.Column(db.Text)
    visiting_hours = db.Column(db.String(200))
    entry_fee = db.Column(db.String(100))
    best_time_to_visit = db.Column(db.String(200))
    nearby_facilities = db.Column(db.Text)
    image_url = db.Column(db.String(500))
    audio_description_en = db.Column(db.String(500))
    audio_description_ta = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class MedicinalPlant(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    name_tamil = db.Column(db.String(200))
    scientific_name = db.Column(db.String(200))
    family = db.Column(db.String(100))
    medicinal_properties = db.Column(db.Text)
    traditional_uses = db.Column(db.Text)
    siddha_applications = db.Column(db.Text)
    ayurvedic_uses = db.Column(db.Text)
    preparation_methods = db.Column(db.Text)
    safety_warnings = db.Column(db.Text)
    seasonal_availability = db.Column(db.String(200))
    conservation_status = db.Column(db.String(100))
    habitat = db.Column(db.String(200))
    image_url = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UserProfile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(100), unique=True, nullable=False)
    favorite_sites = db.Column(db.Text)  # JSON string of site IDs
    identified_plants = db.Column(db.Text)  # JSON string of plant data
    preferences = db.Column(db.Text)  # JSON string of user preferences
    language_preference = db.Column(db.String(10), default='en')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_active = db.Column(db.DateTime, default=datetime.utcnow)

# Tamil Nadu travel guide system prompt
SYSTEM_PROMPT = """You are "Vaanga Suthalam" (வாங்க சுத்தலாம்), a friendly Tamil Nadu travel and agriculture guide chatbot specifically designed to help foreign tourists and agricultural enthusiasts explore Tamil Nadu, India. Your name means "Come, let's travel" in Tamil.

Your expertise includes:

TRAVEL GUIDANCE:
- Popular destinations (Chennai, Madurai, Kanyakumari, Ooty, Kodaikanal, Pondicherry, Thanjavur, etc.)
- Cultural sites (temples, palaces, museums)
- Local cuisine and food recommendations
- Transportation options (trains, buses, taxis, auto-rickshaws)
- Accommodation suggestions for different budgets
- Cultural etiquette and customs
- Weather and best times to visit
- Local festivals and events
- Shopping areas and markets
- Safety tips for foreign travelers
- Basic Tamil phrases that might be helpful

AGRICULTURAL EXPERTISE:
- Tamil Nadu's major crops (rice, sugarcane, cotton, groundnut, coconut, etc.)
- Traditional farming practices and modern techniques
- Agricultural seasons and crop calendars
- Plant diseases and pest management
- Soil types and agricultural zones in Tamil Nadu
- Agricultural tourism and farm visits
- Organic farming practices
- Agricultural markets and cooperatives
- Government schemes for farmers
- Agricultural research institutions in Tamil Nadu

Always provide:
- Practical, actionable advice
- Budget-friendly and luxury options when relevant
- Cultural context to enhance understanding
- Safety considerations
- Local insights that guidebooks might miss
- Scientific and practical agricultural information
- Sustainable farming recommendations

Be warm, welcoming, and enthusiastic about Tamil Nadu's rich culture, heritage, and agricultural traditions. Use simple English suitable for international visitors and agricultural enthusiasts."""

# Enhanced medicinal plant analysis system prompt
MEDICINAL_PLANT_PROMPT = """You are an expert botanist and traditional medicine specialist with deep knowledge of Tamil Nadu's medicinal plants, Siddha medicine, and Ayurvedic practices. Analyze the provided plant/herb image and provide comprehensive information.

Your analysis should include:

1. PLANT IDENTIFICATION:
   - Scientific name and botanical family
   - Common names in English and Tamil (if known)
   - Physical characteristics and identification features
   - Native habitat and distribution in Tamil Nadu

2. MEDICINAL PROPERTIES:
   - Active compounds and phytochemicals
   - Therapeutic properties (anti-inflammatory, antimicrobial, etc.)
   - Parts used for medicine (leaves, roots, bark, flowers, etc.)
   - Potency and effectiveness ratings

3. TRADITIONAL MEDICINE APPLICATIONS:
   - Siddha medicine uses and formulations
   - Ayurvedic applications and combinations
   - Traditional Tamil folk medicine practices
   - Historical references and ancient texts

4. PREPARATION METHODS:
   - Traditional preparation techniques
   - Dosage forms (decoction, powder, paste, oil, etc.)
   - Proper collection and processing methods
   - Storage and preservation techniques

5. SAFETY AND PRECAUTIONS:
   - Contraindications and side effects
   - Safe dosage guidelines
   - Drug interactions and warnings
   - Pregnancy and children safety considerations
   - Proper identification warnings (toxic look-alikes)

6. CONSERVATION AND SUSTAINABILITY:
   - Conservation status in Tamil Nadu
   - Sustainable harvesting practices
   - Cultivation possibilities
   - Seasonal availability and collection times

7. CULTURAL CONTEXT:
   - Role in Tamil culture and traditions
   - Religious or ceremonial significance
   - Local names and folklore
   - Regional variations in usage

Always emphasize safety first, recommend consulting qualified practitioners, and provide accurate scientific information alongside traditional knowledge."""

# Heritage site information system prompt
HERITAGE_PROMPT = """You are a Tamil Nadu heritage and archaeology expert with extensive knowledge of the state's temples, palaces, monuments, and historical sites. Provide detailed information about heritage sites, their significance, and visitor guidance.

Your expertise covers:

1. HISTORICAL SIGNIFICANCE:
   - Historical periods and dynasties (Chola, Pandya, Pallava, Vijayanagara, etc.)
   - Architectural styles and features
   - Cultural and religious importance
   - Archaeological discoveries and inscriptions

2. ARCHITECTURAL DETAILS:
   - Construction techniques and materials
   - Artistic elements and sculptures
   - Unique features and innovations
   - Restoration and conservation efforts

3. CULTURAL CONTEXT:
   - Religious practices and festivals
   - Local legends and stories
   - Traditional arts and crafts associated
   - Community involvement and traditions

4. VISITOR INFORMATION:
   - Best times to visit and photography tips
   - Dress codes and behavioral guidelines
   - Accessibility and facilities
   - Nearby attractions and accommodations

5. PRESERVATION AWARENESS:
   - Conservation challenges and efforts
   - Visitor responsibility and ethics
   - Supporting local communities
   - Sustainable tourism practices

Provide engaging, educational content that helps visitors appreciate Tamil Nadu's rich heritage while promoting responsible tourism."""

# Agricultural image analysis system prompt
AGRI_VISION_PROMPT = """You are an expert agricultural consultant specializing in Tamil Nadu's agriculture. Analyze the provided image of a plant, leaf, tree, or crop and provide a comprehensive report.

Your analysis should include:

1. PLANT IDENTIFICATION:
   - Scientific name and common names (English and Tamil if applicable)
   - Plant family and characteristics
   - Whether it's commonly grown in Tamil Nadu

2. HEALTH ASSESSMENT:
   - Overall plant health status
   - Any visible diseases, pests, or nutrient deficiencies
   - Signs of stress (water, heat, nutrient, etc.)

3. AGRICULTURAL INSIGHTS:
   - Growing conditions and requirements
   - Best practices for cultivation in Tamil Nadu climate
   - Suitable seasons for planting/harvesting
   - Common challenges and solutions

4. DISEASE/PEST MANAGEMENT (if applicable):
   - Specific disease or pest identification
   - Organic and chemical treatment options
   - Prevention strategies
   - Expected recovery timeline

5. RECOMMENDATIONS:
   - Immediate actions needed
   - Long-term care suggestions
   - Fertilizer or nutrient recommendations
   - Irrigation and care tips

6. TAMIL NADU CONTEXT:
   - How this crop fits into local agriculture
   - Market value and commercial viability
   - Government schemes or support available
   - Local agricultural practices

Provide practical, actionable advice that farmers and agricultural enthusiasts can implement. Be specific about treatments, timelines, and expected outcomes."""

# Initialize database and sample data
def init_db():
    with app.app_context():
        db.create_all()

        # Add heritage sites from comprehensive database if not exists
        if HeritageSite.query.count() == 0:
            for site_data in HERITAGE_SITES_DATA:
                site = HeritageSite(**site_data)
                db.session.add(site)

            db.session.commit()

        # Add medicinal plants data if not exists
        if MedicinalPlant.query.count() == 0:
            for plant_data in MEDICINAL_PLANTS_DATA:
                plant = MedicinalPlant(**plant_data)
                db.session.add(plant)

            db.session.commit()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/heritage-sites')
def get_heritage_sites():
    try:
        lat = request.args.get('lat', type=float)
        lng = request.args.get('lng', type=float)
        radius = request.args.get('radius', 50, type=int)  # km
        category = request.args.get('category', '')

        sites = HeritageSite.query.all()

        # Filter by category if specified
        if category:
            sites = [site for site in sites if site.category == category]

        # Calculate distances if location provided
        if lat and lng:
            user_location = (lat, lng)
            sites_with_distance = []

            for site in sites:
                site_location = (site.latitude, site.longitude)
                distance = geodesic(user_location, site_location).kilometers

                if distance <= radius:
                    site_dict = {
                        'id': site.id,
                        'name': site.name,
                        'name_tamil': site.name_tamil,
                        'category': site.category,
                        'latitude': site.latitude,
                        'longitude': site.longitude,
                        'district': site.district,
                        'description': site.description,
                        'historical_significance': site.historical_significance,
                        'visiting_hours': site.visiting_hours,
                        'entry_fee': site.entry_fee,
                        'best_time_to_visit': site.best_time_to_visit,
                        'nearby_facilities': site.nearby_facilities,
                        'distance': round(distance, 2)
                    }
                    sites_with_distance.append(site_dict)

            # Sort by distance
            sites_with_distance.sort(key=lambda x: x['distance'])
            return jsonify({'sites': sites_with_distance, 'status': 'success'})

        else:
            # Return all sites without distance calculation
            sites_list = []
            for site in sites:
                site_dict = {
                    'id': site.id,
                    'name': site.name,
                    'name_tamil': site.name_tamil,
                    'category': site.category,
                    'latitude': site.latitude,
                    'longitude': site.longitude,
                    'district': site.district,
                    'description': site.description,
                    'historical_significance': site.historical_significance,
                    'visiting_hours': site.visiting_hours,
                    'entry_fee': site.entry_fee,
                    'best_time_to_visit': site.best_time_to_visit,
                    'nearby_facilities': site.nearby_facilities
                }
                sites_list.append(site_dict)

            return jsonify({'sites': sites_list, 'status': 'success'})

    except Exception as e:
        return jsonify({'error': str(e), 'status': 'error'}), 500

@app.route('/medicinal-plants')
def get_medicinal_plants():
    try:
        search_term = request.args.get('search', '')
        category = request.args.get('category', '')

        plants = MedicinalPlant.query.all()

        # Filter by search term if provided
        if search_term:
            plants = [plant for plant in plants if
                     search_term.lower() in plant.name.lower() or
                     search_term.lower() in plant.name_tamil.lower() or
                     search_term.lower() in plant.scientific_name.lower()]

        plants_list = []
        for plant in plants:
            plant_dict = {
                'id': plant.id,
                'name': plant.name,
                'name_tamil': plant.name_tamil,
                'scientific_name': plant.scientific_name,
                'family': plant.family,
                'medicinal_properties': plant.medicinal_properties,
                'traditional_uses': plant.traditional_uses,
                'siddha_applications': plant.siddha_applications,
                'ayurvedic_uses': plant.ayurvedic_uses,
                'preparation_methods': plant.preparation_methods,
                'safety_warnings': plant.safety_warnings,
                'seasonal_availability': plant.seasonal_availability,
                'conservation_status': plant.conservation_status,
                'habitat': plant.habitat
            }
            plants_list.append(plant_dict)

        return jsonify({'plants': plants_list, 'status': 'success'})

    except Exception as e:
        return jsonify({'error': str(e), 'status': 'error'}), 500

@app.route('/forest-analysis', methods=['POST'])
def forest_analysis():
    try:
        # Enhanced forest and botanical analysis
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        analysis_focus = request.form.get('focus', 'general')  # general, medicinal, ecological

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Please upload an image file.'}), 400

        # Read and process the image
        image_data = file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Enhanced prompt based on analysis focus
        if analysis_focus == 'medicinal':
            system_prompt = MEDICINAL_PLANT_PROMPT
            user_text = """Analyze this forest/botanical image with focus on medicinal properties. Provide:
            1. Plant identification with scientific and Tamil names
            2. Medicinal properties and traditional uses in Tamil Nadu
            3. Siddha and Ayurvedic applications
            4. Preparation methods and dosage
            5. Safety warnings and contraindications
            6. Conservation status and sustainable harvesting
            7. Cultural significance in Tamil traditions"""

        elif analysis_focus == 'ecological':
            system_prompt = """You are a forest ecologist and botanist specializing in Tamil Nadu's ecosystems.
            Analyze forest plants and trees for their ecological importance, biodiversity value, and conservation status."""
            user_text = """Analyze this forest image for ecological significance. Provide:
            1. Species identification and ecological role
            2. Habitat requirements and distribution
            3. Ecological interactions (pollinators, seed dispersers)
            4. Conservation status and threats
            5. Role in forest ecosystem and biodiversity
            6. Climate change adaptation
            7. Forest management recommendations"""

        else:
            system_prompt = MEDICINAL_PLANT_PROMPT
            user_text = """Provide comprehensive botanical analysis including:
            1. Plant identification (scientific, English, Tamil names)
            2. Botanical characteristics and family
            3. Habitat and distribution in Tamil Nadu
            4. Traditional and medicinal uses
            5. Ecological importance
            6. Conservation status
            7. Cultural significance"""

        # Create chat completion with vision
        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": user_text
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url,
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1500,
            temperature=0.2
        )

        analysis_result = response.choices[0].message.content.strip()

        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'analysis_focus': analysis_focus,
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error in forest analysis: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Create chat completion with OpenAI
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": user_message}
            ],
            max_tokens=500,
            temperature=0.7
        )

        bot_response = response.choices[0].message.content.strip()
        
        return jsonify({
            'response': bot_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'An error occurred: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/analyze-medicinal-plant', methods=['POST'])
def analyze_medicinal_plant():
    try:
        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Please upload an image file.'}), 400

        # Read and process the image
        image_data = file.read()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare the image for OpenAI Vision API
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Create chat completion with vision for medicinal plant analysis
        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "system",
                    "content": MEDICINAL_PLANT_PROMPT
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Please analyze this plant/herb image and provide comprehensive information about its medicinal properties, traditional uses in Tamil Nadu, Siddha medicine applications, and safety guidelines."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url,
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1200,
            temperature=0.2
        )

        analysis_result = response.choices[0].message.content.strip()

        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'analysis_type': 'medicinal_plant',
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error analyzing medicinal plant: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/analyze-image', methods=['POST'])
def analyze_image():
    try:
        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Get analysis type
        analysis_type = request.form.get('analysis_type', 'agricultural')

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Please upload an image file.'}), 400

        # Read and process the image
        image_data = file.read()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare the image for OpenAI Vision API
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Choose appropriate prompt based on analysis type
        if analysis_type == 'medicinal':
            system_prompt = MEDICINAL_PLANT_PROMPT
            user_text = "Please analyze this plant/herb image and provide comprehensive information about its medicinal properties, traditional uses in Tamil Nadu, Siddha medicine applications, and safety guidelines."
        else:
            system_prompt = AGRI_VISION_PROMPT
            user_text = "Please analyze this agricultural image and provide a comprehensive report about the plant, its health, and recommendations for Tamil Nadu farming conditions."

        # Create chat completion with vision
        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": user_text
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url,
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1200,
            temperature=0.3
        )

        analysis_result = response.choices[0].message.content.strip()

        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'analysis_type': analysis_type,
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error analyzing image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/chat-with-image', methods=['POST'])
def chat_with_image():
    try:
        # Get both message and image
        user_message = request.form.get('message', '')

        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Process image
        image_data = file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else 'jpg'
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Create chat completion with both text and image
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]

        # Add user message with image
        user_content = [
            {
                "type": "text",
                "text": user_message if user_message else "Please analyze this agricultural image and tell me about it in the context of Tamil Nadu farming."
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": image_url,
                    "detail": "high"
                }
            }
        ]

        messages.append({
            "role": "user",
            "content": user_content
        })

        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=messages,
            max_tokens=800,
            temperature=0.5
        )

        bot_response = response.choices[0].message.content.strip()

        return jsonify({
            'response': bot_response,
            'status': 'success',
            'has_image': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error processing chat with image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    try:
        data = request.get_json()
        text = data.get('text', '')
        language = data.get('language', 'en')  # en, ta, hi

        if not text:
            return jsonify({'error': 'No text provided'}), 400

        # Language mapping for gTTS
        lang_map = {
            'en': 'en',
            'ta': 'ta',
            'hi': 'hi'
        }

        tts_lang = lang_map.get(language, 'en')

        # Generate speech
        tts = gTTS(text=text, lang=tts_lang, slow=False)

        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
        tts.save(temp_file.name)

        return send_file(temp_file.name, as_attachment=True, download_name='speech.mp3', mimetype='audio/mpeg')

    except Exception as e:
        return jsonify({'error': f'Error generating speech: {str(e)}', 'status': 'error'}), 500

@app.route('/process-voice', methods=['POST'])
def process_voice():
    try:
        if 'audio' not in request.files:
            return jsonify({'error': 'No audio file provided'}), 400

        audio_file = request.files['audio']
        language = request.form.get('language', 'en')

        # Save audio to temporary file
        temp_audio = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
        audio_file.save(temp_audio.name)

        # Initialize speech recognition
        recognizer = sr.Recognizer()

        with sr.AudioFile(temp_audio.name) as source:
            audio_data = recognizer.record(source)

        # Language mapping for speech recognition
        lang_map = {
            'en': 'en-US',
            'ta': 'ta-IN',
            'hi': 'hi-IN'
        }

        recognition_lang = lang_map.get(language, 'en-US')

        # Recognize speech
        try:
            text = recognizer.recognize_google(audio_data, language=recognition_lang)

            # Clean up temporary file
            os.unlink(temp_audio.name)

            return jsonify({
                'text': text,
                'language': language,
                'status': 'success'
            })

        except sr.UnknownValueError:
            return jsonify({'error': 'Could not understand audio', 'status': 'error'}), 400
        except sr.RequestError as e:
            return jsonify({'error': f'Speech recognition error: {str(e)}', 'status': 'error'}), 500

    except Exception as e:
        return jsonify({'error': f'Error processing voice: {str(e)}', 'status': 'error'}), 500

@app.route('/get-weather')
def get_weather():
    try:
        lat = request.args.get('lat', type=float)
        lng = request.args.get('lng', type=float)
        location = request.args.get('location', '')

        # This is a placeholder for weather API integration
        # You would integrate with OpenWeatherMap or similar service
        weather_data = {
            'location': location or f"{lat}, {lng}",
            'temperature': '28°C',
            'condition': 'Partly Cloudy',
            'humidity': '65%',
            'wind_speed': '12 km/h',
            'forecast': [
                {'day': 'Today', 'high': '32°C', 'low': '24°C', 'condition': 'Sunny'},
                {'day': 'Tomorrow', 'high': '30°C', 'low': '23°C', 'condition': 'Cloudy'},
                {'day': 'Day After', 'high': '29°C', 'low': '22°C', 'condition': 'Light Rain'}
            ]
        }

        return jsonify({'weather': weather_data, 'status': 'success'})

    except Exception as e:
        return jsonify({'error': str(e), 'status': 'error'}), 500

@app.route('/user-profile', methods=['GET', 'POST'])
def user_profile():
    try:
        session_id = request.args.get('session_id') or request.json.get('session_id') if request.json else None

        if not session_id:
            return jsonify({'error': 'Session ID required'}), 400

        if request.method == 'GET':
            # Get user profile
            profile = UserProfile.query.filter_by(session_id=session_id).first()
            if profile:
                return jsonify({
                    'profile': {
                        'session_id': profile.session_id,
                        'favorite_sites': json.loads(profile.favorite_sites or '[]'),
                        'identified_plants': json.loads(profile.identified_plants or '[]'),
                        'preferences': json.loads(profile.preferences or '{}'),
                        'language_preference': profile.language_preference
                    },
                    'status': 'success'
                })
            else:
                return jsonify({'error': 'Profile not found', 'status': 'error'}), 404

        elif request.method == 'POST':
            # Update user profile
            data = request.get_json()
            profile = UserProfile.query.filter_by(session_id=session_id).first()

            if not profile:
                profile = UserProfile(session_id=session_id)
                db.session.add(profile)

            if 'favorite_sites' in data:
                profile.favorite_sites = json.dumps(data['favorite_sites'])
            if 'identified_plants' in data:
                profile.identified_plants = json.dumps(data['identified_plants'])
            if 'preferences' in data:
                profile.preferences = json.dumps(data['preferences'])
            if 'language_preference' in data:
                profile.language_preference = data['language_preference']

            profile.last_active = datetime.utcnow()
            db.session.commit()

            return jsonify({'status': 'success', 'message': 'Profile updated'})

    except Exception as e:
        return jsonify({'error': str(e), 'status': 'error'}), 500

@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'service': 'Vaanga Suthalam Heritage & Forest Explorer'})

if __name__ == '__main__':
    init_db()
    app.run(debug=True, host='0.0.0.0', port=5000)

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaanga Suthalam - Tamil Nadu Travel Guide</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Background Elements -->
    <div class="bg-pattern"></div>
    <div class="bg-gradient"></div>

    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <i class="fas fa-temple"></i>
                    </div>
                    <div class="logo-text">
                        <h1>Vaanga Suthalam</h1>
                        <p>வாங்க சுத்தலாம்</p>
                    </div>
                </div>
            </div>

            <div class="sidebar-content">
                <div class="travel-stats">
                    <div class="stat-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <span class="stat-number">32</span>
                            <span class="stat-label">Districts</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-temple"></i>
                        <div>
                            <span class="stat-number">38,000+</span>
                            <span class="stat-label">Temples</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-utensils"></i>
                        <div>
                            <span class="stat-number">100+</span>
                            <span class="stat-label">Dishes</span>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Popular Topics</h3>
                    <div class="action-buttons">
                        <button class="action-btn" data-topic="temples">
                            <i class="fas fa-temple"></i>
                            <span>Temples</span>
                        </button>
                        <button class="action-btn" data-topic="food">
                            <i class="fas fa-bowl-food"></i>
                            <span>Cuisine</span>
                        </button>
                        <button class="action-btn" data-topic="transport">
                            <i class="fas fa-train"></i>
                            <span>Transport</span>
                        </button>
                        <button class="action-btn" data-topic="culture">
                            <i class="fas fa-masks-theater"></i>
                            <span>Culture</span>
                        </button>
                        <button class="action-btn" data-topic="places">
                            <i class="fas fa-mountain"></i>
                            <span>Places</span>
                        </button>
                        <button class="action-btn" data-topic="shopping">
                            <i class="fas fa-shopping-bag"></i>
                            <span>Shopping</span>
                        </button>
                        <button class="action-btn" data-topic="agriculture">
                            <i class="fas fa-seedling"></i>
                            <span>Agriculture</span>
                        </button>
                        <button class="action-btn" data-topic="crops">
                            <i class="fas fa-wheat-awn"></i>
                            <span>Crops</span>
                        </button>
                        <button class="action-btn" data-topic="heritage">
                            <i class="fas fa-landmark"></i>
                            <span>Heritage</span>
                        </button>
                        <button class="action-btn" data-topic="forest">
                            <i class="fas fa-tree"></i>
                            <span>Forest</span>
                        </button>
                    </div>
                </div>

                <!-- Heritage Sites Section -->
                <div class="heritage-section">
                    <h3>🏛️ Heritage Explorer</h3>
                    <p>Discover Tamil Nadu's rich cultural heritage</p>
                    <button class="heritage-btn" id="findHeritageSites">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Find Nearby Sites</span>
                    </button>
                </div>

                <!-- Forest & Botanical Analysis -->
                <div class="forest-analysis">
                    <h3>🌿 Forest Explorer</h3>
                    <p>Identify medicinal plants and forest species</p>
                    <button class="forest-upload-btn" id="openForestAnalysis">
                        <i class="fas fa-leaf"></i>
                        <span>Analyze Forest Plant</span>
                    </button>
                </div>

                <!-- Voice Assistant -->
                <div class="voice-assistant">
                    <h3>🎤 Voice Assistant</h3>
                    <p>Speak in Tamil, English, or Hindi</p>
                    <button class="voice-btn" id="startVoiceInput">
                        <i class="fas fa-microphone"></i>
                        <span>Start Voice Chat</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Chat Area -->
        <main class="chat-main">
            <!-- Chat Header -->
            <div class="chat-header">
                <div class="chat-header-info">
                    <div class="bot-avatar">
                        <i class="fas fa-robot"></i>
                        <div class="status-indicator"></div>
                    </div>
                    <div class="bot-info">
                        <h2>Vaanga Suthalam - Heritage & Forest Explorer</h2>
                        <p class="bot-status">
                            <i class="fas fa-circle"></i>
                            Online - Ready to explore Tamil Nadu's heritage and nature
                        </p>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="action-icon" id="voiceToggle" title="Voice Assistant">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button class="action-icon" id="locationBtn" title="Find Heritage Sites">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button class="action-icon" id="clearChat" title="Clear Chat">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="action-icon" id="toggleSidebar" title="Toggle Sidebar">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>

            <!-- Welcome Card -->
            <div class="welcome-card" id="welcomeCard">
                <div class="welcome-content">
                    <div class="welcome-icon">
                        <i class="fas fa-hands-praying"></i>
                    </div>
                    <h2>வணக்கம்! Welcome to Tamil Nadu!</h2>
                    <p>I'm your AI travel companion, ready to guide you through the incredible journey of exploring Tamil Nadu. From ancient temples to delicious cuisine, I'm here to help!</p>

                    <div class="welcome-suggestions">
                        <div class="suggestion-card" data-message="What are the must-visit temples in Tamil Nadu?">
                            <i class="fas fa-temple"></i>
                            <span>Must-visit Temples</span>
                        </div>
                        <div class="suggestion-card" data-message="What are the famous foods I should try in Tamil Nadu?">
                            <i class="fas fa-bowl-food"></i>
                            <span>Local Cuisine</span>
                        </div>
                        <div class="suggestion-card" data-message="How do I travel between cities in Tamil Nadu?">
                            <i class="fas fa-train"></i>
                            <span>Transportation</span>
                        </div>
                        <div class="suggestion-card" data-message="What's the best time to visit Tamil Nadu?">
                            <i class="fas fa-calendar"></i>
                            <span>Best Time to Visit</span>
                        </div>
                        <div class="suggestion-card" data-message="Tell me about Tamil Nadu's agriculture and major crops">
                            <i class="fas fa-seedling"></i>
                            <span>Agriculture & Crops</span>
                        </div>
                        <div class="suggestion-card" data-message="What are the famous agricultural regions in Tamil Nadu?">
                            <i class="fas fa-tractor"></i>
                            <span>Farming Regions</span>
                        </div>
                        <div class="suggestion-card" data-message="Show me UNESCO World Heritage Sites in Tamil Nadu">
                            <i class="fas fa-landmark"></i>
                            <span>Heritage Sites</span>
                        </div>
                        <div class="suggestion-card" data-message="Tell me about medicinal plants found in Tamil Nadu forests">
                            <i class="fas fa-leaf"></i>
                            <span>Medicinal Plants</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Messages Container -->
            <div class="chat-messages-container">
                <div class="chat-messages" id="chatMessages">
                    <!-- Messages will be dynamically added here -->
                </div>

                <!-- Typing Indicator -->
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <div class="typing-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="typing-content">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="typing-text">Assistant is thinking...</span>
                    </div>
                </div>
            </div>

            <!-- Chat Input Area -->
            <div class="chat-input-area">
                <!-- Image Preview Area -->
                <div class="image-preview-container" id="imagePreviewContainer" style="display: none;">
                    <div class="image-preview">
                        <img id="previewImage" src="" alt="Preview">
                        <div class="image-info">
                            <span class="image-name" id="imageName"></span>
                            <button class="remove-image" id="removeImage">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="input-container">
                    <div class="input-wrapper">
                        <button class="attachment-btn" id="attachmentBtn" title="Upload Image">
                            <i class="fas fa-camera"></i>
                        </button>
                        <textarea id="messageInput" placeholder="Ask me about Tamil Nadu travel or agriculture..." maxlength="500" rows="1"></textarea>
                        <div class="input-actions">
                            <span class="char-count" id="charCount">0/500</span>
                            <button id="sendButton" type="button" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Hidden file input -->
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                </div>

                <!-- Quick Reply Suggestions -->
                <div class="quick-replies" id="quickReplies">
                    <div class="quick-reply" data-message="Tell me about Chennai">Chennai</div>
                    <div class="quick-reply" data-message="Best temples to visit">Temples</div>
                    <div class="quick-reply" data-message="Tamil food recommendations">Food</div>
                    <div class="quick-reply" data-message="How to get around">Transport</div>
                    <div class="quick-reply" data-message="Tamil Nadu agriculture">Agriculture</div>
                    <div class="quick-reply" data-message="Major crops grown">Crops</div>
                    <div class="quick-reply" data-message="Heritage sites near me">Heritage</div>
                    <div class="quick-reply" data-message="Forest medicinal plants">Forest Plants</div>
                </div>
            </div>
        </main>
    </div>

    <!-- Heritage Sites Modal -->
    <div class="modal-overlay" id="heritageSitesModal" style="display: none;">
        <div class="modal-content heritage-modal">
            <div class="modal-header">
                <h3>🏛️ Tamil Nadu Heritage Sites</h3>
                <button class="modal-close" id="closeHeritageModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="heritage-filters">
                    <button class="filter-btn active" data-category="all">All Sites</button>
                    <button class="filter-btn" data-category="temple">Temples</button>
                    <button class="filter-btn" data-category="palace">Palaces</button>
                    <button class="filter-btn" data-category="fort">Forts</button>
                </div>
                <div class="heritage-sites-list" id="heritageSitesList">
                    <!-- Heritage sites will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Voice Assistant Modal -->
    <div class="modal-overlay" id="voiceModal" style="display: none;">
        <div class="modal-content voice-modal">
            <div class="modal-header">
                <h3>🎤 Voice Assistant</h3>
                <button class="modal-close" id="closeVoiceModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="voice-controls">
                    <div class="language-selector">
                        <label>Select Language:</label>
                        <select id="voiceLanguage">
                            <option value="en">English</option>
                            <option value="ta">Tamil (தமிழ்)</option>
                            <option value="hi">Hindi (हिंदी)</option>
                        </select>
                    </div>

                    <div class="voice-status" id="voiceStatus">
                        <i class="fas fa-microphone-slash"></i>
                        <span>Click to start voice input</span>
                    </div>

                    <div class="voice-buttons">
                        <button class="voice-record-btn" id="startRecording">
                            <i class="fas fa-microphone"></i>
                            <span>Start Recording</span>
                        </button>
                        <button class="voice-stop-btn" id="stopRecording" style="display: none;">
                            <i class="fas fa-stop"></i>
                            <span>Stop Recording</span>
                        </button>
                    </div>

                    <div class="voice-transcript" id="voiceTranscript" style="display: none;">
                        <h4>Recognized Text:</h4>
                        <p id="transcriptText"></p>
                        <button class="send-transcript-btn" id="sendTranscript">
                            <i class="fas fa-paper-plane"></i>
                            Send Message
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Upload Modal -->
    <div class="modal-overlay" id="imageUploadModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🌱 Agricultural Image Analysis</h3>
                <button class="modal-close" id="closeImageModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h4>Upload Plant/Crop Image</h4>
                        <p>Drag and drop an image here, or click to select</p>
                        <p class="upload-note">Supported formats: JPG, PNG, GIF, WebP (Max 10MB)</p>
                        <input type="file" id="modalImageInput" accept="image/*" style="display: none;">
                        <button class="upload-btn" id="selectImageBtn">
                            <i class="fas fa-folder-open"></i>
                            Select Image
                        </button>
                    </div>
                </div>

                <div class="analysis-options">
                    <h4>Analysis Type</h4>
                    <div class="option-buttons">
                        <button class="option-btn active" data-type="full">
                            <i class="fas fa-microscope"></i>
                            Full Analysis
                        </button>
                        <button class="option-btn" data-type="disease">
                            <i class="fas fa-bug"></i>
                            Disease Check
                        </button>
                        <button class="option-btn" data-type="identify">
                            <i class="fas fa-search"></i>
                            Plant ID
                        </button>
                        <button class="option-btn" data-type="medicinal">
                            <i class="fas fa-pills"></i>
                            Medicinal Analysis
                        </button>
                        <button class="option-btn" data-type="forest">
                            <i class="fas fa-tree"></i>
                            Forest Ecology
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancelUpload">Cancel</button>
                <button class="btn-primary" id="analyzeImage" disabled>
                    <i class="fas fa-search"></i>
                    Analyze Image
                </button>
            </div>
        </div>
    </div>

    <!-- Floating Action Menu -->
    <div class="floating-menu" id="floatingMenu">
        <button class="fab-main" id="fabMain">
            <i class="fas fa-compass"></i>
        </button>
        <div class="fab-options">
            <button class="fab-option" data-action="weather" title="Weather Info">
                <i class="fas fa-cloud-sun"></i>
            </button>
            <button class="fab-option" data-action="emergency" title="Emergency Contacts">
                <i class="fas fa-phone"></i>
            </button>
            <button class="fab-option" data-action="translate" title="Basic Tamil Phrases">
                <i class="fas fa-language"></i>
            </button>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>Connecting to your travel guide...</h3>
            <p>Please wait while we prepare your Tamil Nadu experience</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>

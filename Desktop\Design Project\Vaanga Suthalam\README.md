# Vaanga Suthalam - Tamil Nadu Travel Guide Chatbot

**வாங்க சுத்தலாம்** (Come, let's travel) - An AI-powered travel guide chatbot specifically designed to help foreign tourists explore Tamil Nadu, India.

## Features

- 🤖 **AI-Powered Guidance**: Uses OpenAI GPT to provide intelligent travel recommendations
- 🏛️ **Cultural Expertise**: Specialized knowledge about Tamil Nadu's temples, heritage sites, and culture
- 🍛 **Local Cuisine**: Recommendations for authentic Tamil food and restaurants
- 🚂 **Transportation**: Detailed guidance on trains, buses, and local transport
- 🏨 **Accommodation**: Budget-friendly to luxury hotel suggestions
- 🎭 **Cultural Etiquette**: Tips on local customs and traditions
- 📱 **Responsive Design**: Works perfectly on desktop and mobile devices
- 🌐 **Multilingual Support**: English interface with Tamil cultural context

## Setup Instructions

### Prerequisites

- Python 3.7 or higher
- OpenAI API key
- Internet connection

### Installation

1. **Clone or download the project files**
   ```bash
   cd "Desktop\Design Project\Vaanga Suthalam"
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure OpenAI API Key**
   - Open the `.env` file
   - Replace `your_openai_api_key_here` with your actual OpenAI API key
   ```
   OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the chatbot**
   - Open your web browser
   - Go to `http://localhost:5000`
   - Start chatting with your Tamil Nadu travel guide!

## Getting an OpenAI API Key

1. Visit [OpenAI's website](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to the API section
4. Generate a new API key
5. Copy the key and paste it in your `.env` file

## Usage Examples

Ask the chatbot questions like:
- "What are the must-visit temples in Tamil Nadu?"
- "What local foods should I try in Chennai?"
- "How do I travel from Chennai to Madurai?"
- "What's the best time to visit Ooty?"
- "Tell me about Tamil cultural etiquette"
- "Where can I buy traditional Tamil sarees?"

## Project Structure

```
Vaanga Suthalam/
├── app.py                 # Flask backend with OpenAI integration
├── requirements.txt       # Python dependencies
├── .env                  # Environment variables (API keys)
├── README.md             # This file
├── templates/
│   └── index.html        # Main HTML template
└── static/
    ├── style.css         # CSS styling with Tamil Nadu theme
    └── script.js         # JavaScript for chat functionality
```

## Customization

### Modifying the AI Personality
Edit the `SYSTEM_PROMPT` in `app.py` to customize the chatbot's personality and knowledge focus.

### Styling Changes
Modify `static/style.css` to change colors, fonts, or layout. The current theme uses Tamil Nadu's traditional colors (saffron, red, and gold).

### Adding Features
- Add new endpoints in `app.py` for additional functionality
- Extend the frontend in `static/script.js` for new interactive features
- Modify `templates/index.html` for structural changes

## Troubleshooting

### Common Issues

1. **"OpenAI API key not found"**
   - Make sure your `.env` file contains the correct API key
   - Ensure the key starts with `sk-`

2. **"Connection refused"**
   - Check if the Flask server is running
   - Verify you're accessing `http://localhost:5000`

3. **"API rate limit exceeded"**
   - You've exceeded your OpenAI API usage limits
   - Check your OpenAI account billing and usage

4. **Chatbot responses are slow**
   - This is normal for OpenAI API calls
   - Consider upgrading your OpenAI plan for faster responses

### Support

For technical issues:
1. Check the browser console for JavaScript errors
2. Check the terminal/command prompt for Python errors
3. Verify your internet connection
4. Ensure all dependencies are installed correctly

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to contribute by:
- Adding more Tamil Nadu-specific knowledge
- Improving the UI/UX design
- Adding new features like image support
- Translating to other languages

---

**வாழ்க தமிழ்! வளர்க தமிழ்!** (Long live Tamil! Let Tamil flourish!)
